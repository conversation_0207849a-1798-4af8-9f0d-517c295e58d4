/* --- Global Styles & Resets --- */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Press Start 2P', cursive;
    color: #e0e0e0;
    background-color: #08081a; /* Dark blue/purple base */
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start; /* Start content from top, allow scrolling if needed */
    padding: 20px;
    text-align: center;
    overflow-x: hidden;
}

/* --- Game Title --- */
.game-title-container {
    margin-bottom: 25px; /* Space below title */
    margin-top: 10px;
}

h1 {
    font-size: 2.5em; /* Slightly smaller for balance */
    color: #ff33cc; /* Vibrant Pink */
    text-transform: uppercase;
    letter-spacing: 1px;
    line-height: 1.2;
    /* Simplified text shadow - less demanding */
    text-shadow:
        0 0 5px #ff33cc, /* Main glow */
        0 0 10px rgba(255, 102, 230, 0.5); /* Softer, wider glow */
    /* Remove the pulsing animation for now, it can be added back if performance allows */
    /* animation: pulseTitle 2.5s infinite alternate ease-in-out; */
}

/* If you want to try the pulse animation with simpler shadows:
@keyframes pulseTitle {
    from {
        opacity: 0.9;
        text-shadow:
            0 0 4px #ff33cc,
            0 0 8px rgba(255, 102, 230, 0.4);
    }
    to {
        opacity: 1;
        text-shadow:
            0 0 6px #ff33cc,
            0 0 12px rgba(255, 102, 230, 0.6);
    }
}
*/

/* --- Game Canvas Area --- */
.game-area {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

#gameCanvas {
    /* Critical: Explicitly set display block and dimensions that match JS. */
    /* The CSS should mainly style the *container* of the canvas, not fight its explicit JS sizing. */
    display: block;
    width: 800px;  /* Match JS canvas.width */
    height: 600px; /* Match JS canvas.height */

    background-color: #000000;
    border: 3px solid #00e5e5; /* Neon Cyan border - simpler than full glow */
    border-radius: 5px;

    /* Much lighter box shadow for a subtle screen effect */
    box-shadow:
        0 0 8px rgba(0, 229, 229, 0.5), /* Softer Cyan glow */
        inset 0 0 5px rgba(0, 150, 150, 0.2); /* Subtle inner bevel */

    /* Ensure crisp pixels when scaled - this is good to keep */
    image-rendering: -moz-crisp-edges;
    image-rendering: -webkit-crisp-edges;
    image-rendering: pixelated;
    image-rendering: crisp-edges;

    /* For responsive scaling *of the element container*, if the viewport is smaller
       The internal canvas drawing surface remains 800x600. */
    max-width: 95vw;
    max-height: calc(90vh - 60px); /* Adjust to leave space for title, etc. */
    /* aspect-ratio: 800 / 600;  This can be good, but ensure it doesn't conflict
                                 with explicit width/height if you notice issues.
                                 With explicit width/height it should mostly affect
                                 how max-width/max-height scale it down. */
}

/* --- Controls Info (if moved from canvas to HTML) --- */
.controls-info {
    margin-top: 20px;
    font-size: 0.75em;
    color: #00ff00; /* Neon Green */
    text-shadow: 0 0 5px rgba(0, 255, 0, 0.7);
    letter-spacing: 0.5px;
}

.controls-info p {
    padding: 5px 8px;
    background-color: rgba(10, 10, 25, 0.6);
    border: 1px solid #006600; /* Darker green */
    border-radius: 3px;
    display: inline-block;
}

/* --- Responsive Adjustments --- */
@media (max-width: 850px) {
    h1 {
        font-size: 2em;
    }
    .game-title-container {
        margin-bottom: 20px;
    }
}

@media (max-width: 600px) {
    h1 {
        font-size: 1.6em;
    }
    .controls-info {
        font-size: 0.7em;
    }
    body {
        padding: 10px;
    }
}