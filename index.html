<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stickman Fighter Deluxe - ARCADE EDITION</title>
    <!-- Arcade Font -->
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
    <!-- Link to your new stylesheet -->
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header class="game-title-container">
        <h1>Stickman Fighter Deluxe</h1>
    </header>

    <main class="game-area">
        <canvas id="gameCanvas" width="800" height="600"></canvas>
    </main>

    <footer class="controls-info">
        <!-- The JS currently draws controls on canvas. If you move them to HTML: -->
        <!-- <p>Controls: A/D-Move | W/Space-Jump | J-Attack | Q-Dash | Shift-Run</p> -->
    </footer>

    <!-- Hidden container for GIF/Image elements (if still needed, or for new sprites) -->
    <div id="assetContainer" style="display: none;"></div>

    <!-- Matter.js library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/matter-js/0.19.0/matter.min.js"></script>
    <!-- Your game script -->
    <script src="game.js"></script>
</body>
</html>